# 星河写作 - 需求文档

这是需求文档，记录了项目的架构、功能需求和开发计划。

## 1. 项目结构

### 1.1 技术栈
- **前端框架**: Next.js 14 (App Router)
- **开发语言**: TypeScript
- **UI框架**: Tailwind CSS (吉卜力工作室艺术风格)
- **状态管理**: Zustand
- **数据存储**: IndexedDB (本地) + Supabase (云端)
- **用户认证**: Supabase Auth
- **AI服务**: 多模型支持 (Gemini、Claude等)
- **文件存储**: MinIO
- **富文本编辑**: TipTap

### 1.2 目录结构
```
/src
├── app/                    # Next.js App Router页面
│   ├── api/               # API路由
│   │   ├── ai/           # AI相关API
│   │   ├── auth/         # 认证API
│   │   ├── billing/      # 计费API
│   │   ├── files/        # 文件管理API
│   │   ├── prompt/       # 提示词API
│   │   ├── user/         # 用户API
│   │   └── works/        # 作品API
│   ├── auth/             # 认证相关页面
│   ├── works/            # 作品管理页面
│   ├── prompts/          # 提示词管理页面
│   ├── chat/             # AI对话页面
│   └── knowledgebase/    # 知识库页面
├── components/            # 可复用组件
│   ├── auth/             # 认证组件
│   ├── chat/             # 对话组件
│   ├── works/            # 作品组件
│   ├── prompts/          # 提示词组件
│   ├── knowledgebase/    # 知识库组件
│   └── common/           # 通用组件
├── contexts/             # React Context
├── data/                 # 数据层
│   └── database/         # 数据库操作
├── hooks/                # 自定义Hooks
├── lib/                  # 工具库
│   ├── billing/          # 计费服务
│   └── utils/            # 工具函数
├── services/             # 业务服务
├── store/                # Zustand状态管理
├── types/                # TypeScript类型定义
└── utils/                # 工具函数
    └── supabase/         # Supabase客户端
```

## 2. 项目关键文件的作用

### 2.1 核心配置文件
- **package.json**: 项目依赖和脚本配置，定义了项目名称"zhuguang1.0"
- **next.config.mjs**: Next.js配置，禁用严格模式和构建错误检查
- **tailwind.config.ts**: Tailwind CSS配置，定义吉卜力风格的颜色系统和动画
- **tsconfig.json**: TypeScript编译配置

### 2.2 布局和样式文件
- **src/app/layout.tsx**: 根布局组件，集成认证、导航、AI对话等全局功能
- **src/app/globals.css**: 全局样式，定义吉卜力风格的颜色变量和组件样式
- **src/styles/typing-cursor.css**: 打字机效果样式

### 2.3 数据层文件
- **src/data/database/config.ts**: 数据库配置，定义IndexedDB数据库结构
- **src/data/database/core/**: 数据库核心操作（连接、迁移、操作）
- **src/data/database/repositories/**: 数据仓库层，各实体的CRUD操作
- **src/data/database/types/**: 数据类型定义

### 2.4 认证和用户管理
- **src/utils/supabase/**: Supabase客户端配置（浏览器端、服务端、中间件）
- **src/hooks/useAuth.ts**: 认证Hook，提供登录、注册、登出功能
- **src/components/auth/**: 认证相关组件（登录、注册、保护路由等）

### 2.5 AI服务文件
- **src/lib/AIserver.ts**: AI服务接口，统一管理多个AI模型
- **src/app/api/ai/stream/route.ts**: AI流式API路由，处理AI对话请求
- **src/lib/billing/**: 计费服务，管理token消费和用户余额

### 2.6 核心功能模块
- **src/app/works/**: 作品管理页面和编辑器
- **src/components/works/**: 作品相关组件（编辑器、AI助手、章节管理等）
- **src/app/prompts/**: 提示词管理页面
- **src/components/prompts/**: 提示词相关组件
- **src/app/knowledgebase/**: 知识库管理页面
- **src/components/knowledgebase/**: 知识库相关组件

### 2.7 全局功能
- **src/contexts/**: React Context（导航、AI对话）
- **src/components/chat/**: 全局AI对话功能组件
- **src/store/**: Zustand状态管理
- **middleware.ts**: Next.js中间件，处理认证和路由保护

